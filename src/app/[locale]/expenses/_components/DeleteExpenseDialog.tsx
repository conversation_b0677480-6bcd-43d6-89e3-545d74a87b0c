'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { useTranslations } from 'next-intl';
import { FormattedCurrency, DateTime } from '@/components/common';
import { Expense } from '@/redux/services/expensesApi';

interface DeleteExpenseDialogProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
  isLoading: boolean;
  selectedExpense: Expense | null;
  onCancel: () => void;
}

export function DeleteExpenseDialog({
  isOpen,
  onOpenChange,
  onConfirm,
  isLoading,
  selectedExpense,
  onCancel,
}: DeleteExpenseDialogProps) {
  const t = useTranslations('expenses');

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>{t('dialog.delete.title')}</DialogTitle>
          <DialogDescription>
            {t('dialog.delete.description')}
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          {selectedExpense && (
            <div className="space-y-2">
              <p><strong>{t('dialog.details.date')}</strong> <DateTime date={selectedExpense.date} format="short" /></p>
              <p><strong>{t('dialog.details.description')}</strong> {selectedExpense.description}</p>
              <p><strong>{t('dialog.details.amount')}</strong> <FormattedCurrency amount={selectedExpense.amount} /></p>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            {t('dialog.buttons.cancel')}
          </Button>
          <Button
            type="button"
            variant="destructive"
            onClick={onConfirm}
            disabled={isLoading}
          >
            {isLoading ? t('dialog.buttons.deleting') : t('dialog.buttons.delete')}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
